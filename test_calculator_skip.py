#!/usr/bin/env python3
"""
Test script to verify that the calculator interface allows skipping calculations.

This script tests that users can submit empty calculations or cancel the calculator
without being forced to enter calculations.
"""

import sys
import os
import unittest.mock as mock

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_calculator_skip_behavior():
    """Test that the calculator allows skipping calculations."""
    try:
        from ui.calculator_interface import CalculatorInterface
        
        # Create a calculator interface instance
        calc_interface = CalculatorInterface()
        
        # Mock the show_calculator_interface method to return empty string (simulating user submitting empty calculations)
        with mock.patch.object(calc_interface, 'show_calculator_interface', return_value=""):
            result = calc_interface.get_calculations_for_eei([])  # Empty las_files for testing
            
            # The result should be None (indicating skip/cancel), not forcing retry
            if result is None:
                print("✅ Empty calculations: Calculator correctly allows skipping (returns None)")
                return True
            else:
                print(f"❌ Empty calculations: Calculator should return None but returned: {result}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing calculator skip behavior: {e}")
        return False

def test_calculator_cancel_behavior():
    """Test that the calculator allows cancelling."""
    try:
        from ui.calculator_interface import CalculatorInterface
        
        # Create a calculator interface instance
        calc_interface = CalculatorInterface()
        
        # Mock the show_calculator_interface method to return None (simulating user cancelling)
        with mock.patch.object(calc_interface, 'show_calculator_interface', return_value=None):
            result = calc_interface.get_calculations_for_eei([])  # Empty las_files for testing
            
            # The result should be None (indicating cancel)
            if result is None:
                print("✅ Cancel: Calculator correctly allows cancelling (returns None)")
                return True
            else:
                print(f"❌ Cancel: Calculator should return None but returned: {result}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing calculator cancel behavior: {e}")
        return False

def test_calculator_with_calculations():
    """Test that the calculator works with actual calculations."""
    try:
        from ui.calculator_interface import CalculatorInterface
        
        # Create a calculator interface instance
        calc_interface = CalculatorInterface()
        
        # Mock the show_calculator_interface method to return some calculations
        test_calculations = "# Test calculation\nTEST_LOG = 1.0"
        
        # Mock all the methods that would be called
        with mock.patch.object(calc_interface, 'show_calculator_interface', return_value=test_calculations), \
             mock.patch.object(calc_interface, 'validate_calculation_inputs', return_value={'valid': True}), \
             mock.patch.object(calc_interface, 'execute_calculations_safely', return_value=True):
            
            result = calc_interface.get_calculations_for_eei([])  # Empty las_files for testing
            
            # The result should be the calculations string
            if result == test_calculations:
                print("✅ With calculations: Calculator correctly processes calculations")
                return True
            else:
                print(f"❌ With calculations: Expected '{test_calculations}' but got: {result}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing calculator with calculations: {e}")
        return False

def test_legacy_wrapper_skip():
    """Test that the legacy wrapper correctly handles skip behavior."""
    try:
        from load_multilas_EEI_XCOR_PLOT_Final import get_calculations_for_eei
        
        # Mock the CalculatorInterface to return None (skip)
        with mock.patch('ui.calculator_interface.CalculatorInterface') as mock_calc_class:
            mock_instance = mock_calc_class.return_value
            mock_instance.get_calculations_for_eei.return_value = None
            
            result = get_calculations_for_eei([])  # Empty las_files for testing
            
            # The legacy wrapper should return False when calculator returns None
            if result is False:
                print("✅ Legacy wrapper: Correctly converts None to False for skip behavior")
                return True
            else:
                print(f"❌ Legacy wrapper: Expected False but got: {result}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing legacy wrapper skip: {e}")
        return False

def main():
    """Run all tests."""
    print("="*60)
    print("🧪 TESTING CALCULATOR SKIP FUNCTIONALITY")
    print("="*60)
    
    tests = [
        ("Empty Calculations Skip", test_calculator_skip_behavior),
        ("Cancel Behavior", test_calculator_cancel_behavior),
        ("With Calculations", test_calculator_with_calculations),
        ("Legacy Wrapper Skip", test_legacy_wrapper_skip),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "="*60)
    print("📊 TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Calculator skip functionality is working correctly.")
        print("Users can now submit empty calculations or cancel without being forced to enter calculations.")
        return True
    else:
        print("⚠️ Some tests failed. The skip functionality may need additional work.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
